import type { Song } from '@/models/Song'

// Duration types
export type SetDuration = {
  totalSeconds: number
  formattedDuration: string
  songCount: number
}

export type ShowDuration = {
  totalSeconds: number
  formattedDuration: string
  setCount: number
  songCount: number
}

export function validateDurationSecs(seconds: number | undefined): number {
  if (typeof seconds !== 'number') return 0
  if (isNaN(seconds)) return 0
  return Math.max(0, Math.floor(seconds))
}

export function formatDuration(seconds: number): string {
  const validSeconds = validateDurationSecs(seconds)
  const minutes = Math.floor(validSeconds / 60)
  const remainingSeconds = Math.floor(validSeconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

export function parseDurationInput(minutes: string, seconds: string): number {
  const mins = parseInt(minutes) || 0
  const secs = parseInt(seconds) || 0
  return validateDurationSecs(mins * 60 + secs)
}

export function getDurationParts(durationSecs: number | undefined): {
  minutes: string
  seconds: string
} {
  const validSeconds = validateDurationSecs(durationSecs)
  return {
    minutes: Math.floor(validSeconds / 60).toString(),
    seconds: (validSeconds % 60).toString().padStart(2, '0')
  }
}

export function calculateSetDuration(songs: Song[]): SetDuration {
  const totalSeconds = songs.reduce((total, song) => {
    // Use song duration if available, otherwise use default
    const songDuration =
      validateDurationSecs(song.durationSecs) ||
      Number(
        getComputedStyle(document.documentElement).getPropertyValue(
          '--duration-song-default'
        )
      ) * 60
    return total + songDuration
  }, 0)

  // Get threshold values from CSS variables
  const minMinutes = Number(
    getComputedStyle(document.documentElement).getPropertyValue(
      '--duration-set-min'
    )
  )
  const maxMinutes = Number(
    getComputedStyle(document.documentElement).getPropertyValue(
      '--duration-set-max'
    )
  )

  const totalMinutes = totalSeconds / 60

  return {
    totalSeconds,
    warning: totalMinutes < minMinutes || totalMinutes > maxMinutes,
    error: totalMinutes < minMinutes * 0.8 || totalMinutes > maxMinutes * 1.2
  }
}

export function calculateShowDuration(sets: Song[][]): ShowDuration {
  const setDurations = sets.map(set => calculateSetDuration(set))
  const totalSeconds = setDurations.reduce(
    (total, set) => total + set.totalSeconds,
    0
  )

  const standardDuration =
    Number(
      getComputedStyle(document.documentElement).getPropertyValue(
        '--duration-show-standard'
      )
    ) * 60

  return {
    totalSeconds,
    setDurations,
    warning: Math.abs(totalSeconds - standardDuration) > standardDuration * 0.1,
    error: Math.abs(totalSeconds - standardDuration) > standardDuration * 0.2
  }
}

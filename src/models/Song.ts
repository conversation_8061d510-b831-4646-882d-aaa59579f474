import { ref } from 'vue'
import { getDurationParts } from '@/utils/duration'

// Type derived from the class itself
export type SongData = {
  id: string
  title: string
  shortTitle: string
  artist: string
  durationSecs: number
  bpm: number
  key: string
  notes: string
  isLastSong: boolean
  isEncoreSeparator: boolean
  lastPerformed?: string
  timesPerformed: number
  tags: string[]
  year?: number
  composers: string[]
  createdAt: string
  updatedAt: string
}

export class Song {
  private _id: string
  private _title = ref('')
  private _shortTitle = ref('')
  private _artist = ref('')
  private _durationSecs = ref(0)
  private _bpm = ref(0)
  private _key = ref('')
  private _notes = ref('')
  private _isLastSong = ref(false)
  private _isEncoreSeparator = ref(false)
  private _lastPerformed = ref<string | null>(null)
  private _timesPerformed = ref(0)
  private _tags = ref<string[]>([])
  private _year = ref<number | null>(null)
  private _composers = ref<string[]>([])
  private _createdAt: string
  private _updatedAt: string

  constructor(data?: Partial<SongData>) {
    this._id = data?.id || crypto.randomUUID()
    this._title.value = data?.title || ''
    this._shortTitle.value = data?.shortTitle || ''
    this._artist.value = data?.artist || ''
    this._durationSecs.value = data?.durationSecs || 0
    this._bpm.value = data?.bpm || 0
    this._key.value = data?.key || ''
    this._notes.value = data?.notes || ''
    this._isLastSong.value = data?.isLastSong || false
    this._isEncoreSeparator.value = data?.isEncoreSeparator || false
    this._lastPerformed.value = data?.lastPerformed?.toString() || null
    this._timesPerformed.value = data?.timesPerformed || 0
    this._tags.value = data?.tags || []
    this._year.value = data?.year || null
    this._composers.value = data?.composers || []
    this._createdAt = data?.createdAt || new Date().toISOString()
    this._updatedAt = data?.updatedAt || new Date().toISOString()
  }

  // Getters
  get id(): string {
    return this._id
  }
  get title(): string {
    return this._title.value
  }
  get shortTitle(): string {
    return this._shortTitle.value
  }
  get artist(): string {
    return this._artist.value
  }
  get durationSecs(): number {
    return this._durationSecs.value
  }
  get bpm(): number {
    return this._bpm.value
  }
  get key(): string {
    return this._key.value
  }
  get notes(): string {
    return this._notes.value
  }
  get isLastSong(): boolean {
    return this._isLastSong.value
  }
  get isEncoreSeparator(): boolean {
    return this._isEncoreSeparator.value
  }
  get lastPerformed(): string | null {
    return this._lastPerformed.value
  }
  get timesPerformed(): number {
    return this._timesPerformed.value
  }
  get tags(): string[] {
    return this._tags.value
  }
  get year(): number | null {
    return this._year.value
  }
  get composers(): string[] {
    return this._composers.value
  }
  get createdAt(): string {
    return this._createdAt
  }
  get updatedAt(): string {
    return this._updatedAt
  }

  // Computed properties
  get formattedDuration(): string {
    const { minutes, seconds } = getDurationParts(this._durationSecs.value)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  get displayTitle(): string {
    return this._shortTitle.value || this._title.value
  }

  get isEmpty(): boolean {
    return !this._title.value.trim().length
  }

  // Setters with automatic timestamp updates
  set title(value: string) {
    this._title.value = value
    this.touch()
  }

  set shortTitle(value: string) {
    this._shortTitle.value = value
    this.touch()
  }

  set artist(value: string) {
    this._artist.value = value
    this.touch()
  }

  set durationSecs(value: number) {
    this._durationSecs.value = value
    this.touch()
  }

  set bpm(value: number) {
    this._bpm.value = value
    this.touch()
  }

  set key(value: string) {
    this._key.value = value
    this.touch()
  }

  set notes(value: string) {
    this._notes.value = value
    this.touch()
  }

  set isLastSong(value: boolean) {
    this._isLastSong.value = value
    this.touch()
  }

  set isEncoreSeparator(value: boolean) {
    this._isEncoreSeparator.value = value
    this.touch()
  }

  // Methods
  addTag(tag: string): void {
    if (!this._tags.value.includes(tag)) {
      this._tags.value.push(tag)
      this.touch()
    }
  }

  removeTag(tag: string): void {
    const index = this._tags.value.indexOf(tag)
    if (index !== -1) {
      this._tags.value.splice(index, 1)
      this.touch()
    }
  }

  addComposer(composer: string): void {
    if (!this._composers.value.includes(composer)) {
      this._composers.value.push(composer)
      this.touch()
    }
  }

  removeComposer(composer: string): void {
    const index = this._composers.value.indexOf(composer)
    if (index !== -1) {
      this._composers.value.splice(index, 1)
      this.touch()
    }
  }

  recordPerformance(): void {
    this._lastPerformed.value = new Date().toISOString()
    this._timesPerformed.value++
    this.touch()
  }

  private touch(): void {
    this._updatedAt = new Date().toISOString()
  }

  // Convert to plain object for storage/serialization
  toJSON(): SongData {
    return {
      id: this._id,
      title: this._title.value,
      shortTitle: this._shortTitle.value,
      artist: this._artist.value,
      durationSecs: this._durationSecs.value,
      bpm: this._bpm.value,
      key: this._key.value,
      notes: this._notes.value,
      isLastSong: this._isLastSong.value,
      isEncoreSeparator: this._isEncoreSeparator.value,
      lastPerformed: this._lastPerformed.value || undefined,
      timesPerformed: this._timesPerformed.value,
      tags: this._tags.value,
      year: this._year.value || undefined,
      composers: this._composers.value,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt
    }
  }

  // Create a Song instance from a plain object
  static fromJSON(data: SongData): Song {
    return new Song(data)
  }
}

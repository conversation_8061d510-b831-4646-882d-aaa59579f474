import { ref, onMounted, watch } from 'vue'
import { Song } from '@/classes/Song'
import { useAuth } from '@/composables/useAuth'
import { db } from '@/firebase'
import {
  collection,
  addDoc,
  query,
  where,
  onSnapshot,
  orderBy
} from 'firebase/firestore'

export function useSongs() {
  const songs = ref<Song[]>([])
  const { user } = useAuth()
  let unsubscribe: (() => void) | undefined

  const subscribeSongs = () => {
    // Clean up existing subscription if any
    unsubscribe?.()

    if (!user.value?.uid) {
      songs.value = []
      return
    }

    const songsQuery = query(
      collection(db, 'songs'),
      where('createdBy', '==', user.value.uid),
      orderBy('title')
    )

    unsubscribe = onSnapshot(
      songsQuery,
      snapshot => {
        songs.value = snapshot.docs.map(doc =>
          Song.fromFirestore(doc.data(), doc.id)
        )
      },
      error => {
        console.error('Error fetching songs:', error)
        songs.value = []
      }
    )

    return unsubscribe
  }

  const createSong = async (songData: Partial<Song>): Promise<Song> => {
    if (!user.value?.uid) throw new Error('User not authenticated')

    const newSong = new Song({
      ...songData,
      createdBy: user.value.uid
    })

    const docRef = await addDoc(collection(db, 'songs'), newSong.toFirestore())
    newSong.id = docRef.id
    return newSong
  }

  // Watch for user changes and resubscribe
  watch(
    () => user.value?.uid,
    () => {
      subscribeSongs()
    },
    { immediate: true }
  )

  // Cleanup on unmount
  onMounted(() => {
    return () => unsubscribe?.()
  })

  return {
    songs,
    createSong
  }
}

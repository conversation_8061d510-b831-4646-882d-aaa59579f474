<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useShows } from '@/composables/useShows'
import SetInput from '@/components/SetInput.vue'
import ShowDurationDisplay from '@/components/ShowDurationDisplay.vue'
import PDFSetList from '@/components/PDFSetList.vue'
import { Show } from '@/models/Show'
import { Song } from '@/models/Song'

// Legacy SetList type for backward compatibility
type SetList = {
  id: string
  name: string
  sets: any[]
  hasEncore: boolean
  createdAt: string
  updatedAt: string
}

const router = useRouter()
const route = useRoute()
const { saveShow, updateShow, loadShow } = useShows()

const show = ref<Show>(new Show({
  title: '',
  venue: '',
  act: '',
  date: new Date().toISOString().split('T')[0]
}))

const isPDFPreviewOpen = ref(false)

// Compute duplicate songs across all sets
const duplicateSongs = computed(() => {
  const songMap = new Map<string, number>()
  const duplicates = new Set<string>()

  show.value.sets.forEach(set => {
    set.songs.forEach(song => {
      if (song.title.trim()) {
        const normalizedTitle = song.title.toLowerCase().trim()
        songMap.set(normalizedTitle, (songMap.get(normalizedTitle) || 0) + 1)
        if (songMap.get(normalizedTitle)! > 1) {
          duplicates.add(normalizedTitle)
        }
      }
    })
  })

  return duplicates
})

// Load show data if editing existing show
onMounted(() => {
  const showId = route.params.id as string
  if (showId && showId !== 'new') {
    const loadedShow = loadShow(showId)
    if (loadedShow) {
      show.value = loadedShow
    } else {
      router.push('/')
    }
  }
})

const handleSave = () => {
  const showId = route.params.id as string
  if (showId && showId !== 'new') {
    updateShow(showId, show.value)
  } else {
    saveShow(show.value)
  }
  router.go(-1)
}

const handleCancel = () => {
  router.go(-1)
}

// Helper function for duration display
const getSongSets = (show: Show) => {
  return show.sets.map(set => set.songs)
}

const addSet = () => {
  show.value.addSet()
}

const removeSet = (setIndex: number) => {
  if (show.value.sets.length > 1) {
    show.value.sets.splice(setIndex, 1)
  }
}

const updateSong = (setIndex: number, songIndex: number, field: 'title' | 'key', value: string) => {
  show.value.sets[setIndex].songs[songIndex][field] = value
}

const addSong = (setIndex: number) => {
  // First, find any empty song rows across all sets
  let foundEmptyRow = false
  show.value.sets.forEach((set, currentSetIndex) => {
    const emptyRowIndex = set.songs.findIndex(song => !song.title.trim() && !song.key.trim())
    if (emptyRowIndex !== -1) {
      // Don't remove the empty row in the current set if it's the only empty row
      if (foundEmptyRow || currentSetIndex !== setIndex) {
        set.songs.splice(emptyRowIndex, 1)
      }
      foundEmptyRow = true
    }
  })

  // Add the new empty song to the target set
  show.value.sets[setIndex].songs.push({
    id: crypto.randomUUID(),
    title: '',
    key: ''
  })
}

const removeSong = (setIndex: number, songIndex: number) => {
  show.value.sets[setIndex].songs = show.value.sets[setIndex].songs.filter(
    (_, index) => index !== songIndex
  )
}

const moveSong = (fromSetIndex: number, fromSongIndex: number, toSetIndex: number, toStart: boolean) => {
  const song = { ...show.value.sets[fromSetIndex].songs[fromSongIndex] }

  // Remove from original set
  show.value.sets[fromSetIndex].songs.splice(fromSongIndex, 1)

  // Add to new set
  if (toStart) {
    show.value.sets[toSetIndex].songs.unshift(song)
  } else {
    show.value.sets[toSetIndex].songs.push(song)
  }
}
</script>

<template>
  <div class="show-edit">
    <div class="show-header">
      <h1>{{ route.params.id === 'new' ? 'New Show' : 'Edit Show' }}</h1>
      <ShowDurationDisplay :sets="getSongSets(show)" showLabel />
    </div>

    <div class="form-grid">
      <div class="form-group">
        <label for="venue">Venue</label>
        <input v-model="show.venue" id="venue" type="text" class="input" />
      </div>
      <div class="form-group">
        <label for="act">Act</label>
        <input v-model="show.act" id="act" type="text" class="input" />
      </div>
      <div class="form-group">
        <label for="date">Date</label>
        <input v-model="show.date" id="date" type="date" class="input" />
      </div>
    </div>

    <div class="sets-grid">
      <template v-for="(set, index) in show.sets" :key="set.id">
        <SetInput v-model="set.songs" :set-index="index" :is-last-set="index === show.sets.length - 1"
          :duplicate-songs="duplicateSongs" @update-song="updateSong" @add-song="addSong" @remove-song="removeSong"
          @remove-set="removeSet" @add-set="addSet" @move-song="moveSong" />
      </template>
    </div>

    <div class="actions">
      <BaseButton @click="handleCancel" variant="secondary" class="cancel">Cancel</BaseButton>
      <BaseButton @click="isPDFPreviewOpen = !isPDFPreviewOpen" class="preview">
        {{ isPDFPreviewOpen ? 'Hide Preview' : 'Show Preview' }}
      </BaseButton>
      <BaseButton @click="handleSave" class="save">Save Show</BaseButton>
    </div>

    <!-- PDF Preview Panel -->
    <Transition name="slide">
      <PDFSetList v-if="isPDFPreviewOpen" :gigData="show" @close="isPDFPreviewOpen = false" />
    </Transition>
  </div>
</template>

<style scoped>
.show-edit {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-md);
}

.show-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.form-group label {
  font-size: var(--font-size-sm);
  color: var(--color-text-light);
}

.sets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-md);
}

.button {
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  transition: opacity var(--transition-fast);
}

.button:hover {
  opacity: 0.9;
}

.button.cancel {
  background: var(--color-surface-dark);
  color: var(--color-text-white);
}

.button.save {
  background: var(--color-success);
  color: var(--color-text-white);
}

.button.preview {
  background: var(--color-primary);
  color: var(--color-text-white);
}

.add-set-button {
  background: var(--color-surface);
  border: 2px dashed var(--color-text-light);
  color: var(--color-text-light);
  border-radius: var(--radius-lg);
  padding: var(--space-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
  opacity: 0.7;
  transition: all var(--transition-fast);
  height: 100%;
  min-height: 200px;
}

.add-set-button:hover {
  opacity: 1;
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* Slide animation */
.slide-enter-active,
.slide-leave-active {
  transition: transform var(--transition-normal) ease-out;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
}
</style>